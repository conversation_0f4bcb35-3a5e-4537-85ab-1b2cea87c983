{% comment %}
  Renders a product media gallery. Should be used with 'media-gallery.js'
  Also see 'product-media-modal'

  Accepts:
  - product: {Object} Product liquid object
  - variant_images: {Array} Product images associated with a variant
  - limit: {Number} (optional) When passed, limits the number of media items to render

  Usage:
  {% render 'product-media-gallery' %}
{% endcomment %}

{%- liquid
  if section.settings.hide_variants and variant_images.size == product.media.size
    assign single_media_visible = true
  endif

  if limit == 1
    assign single_media_visible = true
  endif

  assign media_count = product.media.size
  if section.settings.hide_variants and media_count > 1 and variant_images.size > 0
    assign media_count = media_count | minus: variant_images.size | plus: 1
  endif

  if media_count == 1 or single_media_visible
    assign single_media_visible_mobile = true
  endif

  if media_count == 0 or single_media_visible_mobile or section.settings.mobile_thumbnails == 'show' or section.settings.mobile_thumbnails == 'columns' and media_count < 3
    assign hide_mobile_slider = true
  endif

  if section.settings.media_size == 'large'
    assign media_width = 0.65
  elsif section.settings.media_size == 'medium'
    assign media_width = 0.55
  elsif section.settings.media_size == 'small'
    assign media_width = 0.45
  endif
-%}

<media-gallery
  id="MediaGallery-{{ section.id }}"
  role="region"
  {% if section.settings.enable_sticky_info %}
    class="product__column-sticky"
  {% endif %}
  aria-label="{{ 'products.product.media.gallery_viewer' | t }}"
  data-desktop-layout="{{ section.settings.gallery_layout }}"
>
  <div id="GalleryStatus-{{ section.id }}" class="visually-hidden" role="status"></div>
  <slider-component id="GalleryViewer-{{ section.id }}" class="slider-mobile-gutter">
    <a class="skip-to-content-link button visually-hidden quick-add-hidden" href="#ProductInfo-{{ section.id }}">
      {{ 'accessibility.skip_to_product_info' | t }}
    </a>
    <ul
      id="Slider-Gallery-{{ section.id }}"
      class="product__media-list contains-media grid grid--peek list-unstyled slider slider--mobile"
      role="list"
    >
      {%- liquid
        assign selected_variant = product.selected_or_first_available_variant
        assign rendered_media_ids = '' | split: ','
      -%}

      {%- comment -%}
        1. First render the selected variant's featured image
      {%- endcomment -%}
      {%- if selected_variant.featured_media != null -%}
        {%- assign featured_media = selected_variant.featured_media -%}
        {%- assign rendered_media_ids = rendered_media_ids | concat: featured_media.id -%}
        <li
          id="Slide-{{ section.id }}-{{ featured_media.id }}"
          class="product__media-item grid__item slider__slide is-active{% if single_media_visible %} product__media-item--single{% endif %}{% if featured_media.media_type != 'image' %} product__media-item--full{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}"
          data-media-id="{{ section.id }}-{{ featured_media.id }}"
        >
          {%- assign media_position = 1 -%}
          {% render 'product-thumbnail',
            media: featured_media,
            media_count: media_count,
            position: media_position,
            desktop_layout: section.settings.gallery_layout,
            mobile_layout: section.settings.mobile_thumbnails,
            loop: section.settings.enable_video_looping,
            modal_id: section.id,
            xr_button: true,
            media_width: media_width,
            media_fit: section.settings.media_fit,
            constrain_to_viewport: section.settings.constrain_to_viewport,
            lazy_load: false
          %}
        </li>
      {%- endif -%}

      {%- comment -%}
        2. Render ONE secondary image slot in position 2 (initially shows selected variant's secondary image)
      {%- endcomment -%}
      {%- assign has_secondary_images = false -%}
      {%- for variant in product.variants -%}
        {%- if variant.metafields.custom.secondary_image -%}
          {%- assign has_secondary_images = true -%}
          {%- break -%}
        {%- endif -%}
      {%- endfor -%}

      {%- if has_secondary_images -%}
        {%- liquid
          assign media_position = media_position | default: 1 | plus: 1
          assign initial_secondary_image = selected_variant.metafields.custom.secondary_image
        -%}
        <li
          id="Slide-{{ section.id }}-secondary-slot"
          class="product__media-item grid__item slider__slide secondary-media-slot{% if single_media_visible %} product__media-item--single{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}{% unless initial_secondary_image %} secondary-slot-hidden{% endunless %}"
          data-media-id="{{ section.id }}-secondary-slot"
          data-media-position="{{ media_position }}"
        >
          <div
            class="product-media-container media-type-image media-fit-{{ section.settings.media_fit }} global-media-settings gradient{% if section.settings.constrain_to_viewport %} constrain-height{% endif %}"
            id="secondary-image-container-{{ section.id }}"
          >
            {%- if initial_secondary_image -%}
              <modal-opener
                class="product__modal-opener product__modal-opener--image"
                data-modal="#ProductModal-{{ section.id }}"
              >
                <span
                  class="product__media-icon motion-reduce quick-add-hidden{% if section.settings.image_zoom == 'hover' %} product__media-icon--hover{% endif %}"
                  aria-hidden="true"
                >
                  <span class="svg-wrapper">
                    {{- 'icon-zoom.svg' | inline_asset_content -}}
                  </span>
                </span>
                {%- render 'loading-spinner' -%}
                <div class="product__media media media--transparent" id="secondary-image-content-{{ section.id }}">
                  {%- if initial_secondary_image.media_type -%}
                    {{ initial_secondary_image | media_image: class: 'media--adapt', loading: 'lazy' }}
                  {%- else -%}
                    <img
                      class="media--adapt"
                      src="{{ initial_secondary_image | image_url: width: 1946 }}"
                      srcset="
                        {%- if initial_secondary_image.width >= 246 -%}{{ initial_secondary_image | image_url: width: 246 }} 246w,{%- endif -%}
                        {%- if initial_secondary_image.width >= 493 -%}{{ initial_secondary_image | image_url: width: 493 }} 493w,{%- endif -%}
                        {%- if initial_secondary_image.width >= 600 -%}{{ initial_secondary_image | image_url: width: 600 }} 600w,{%- endif -%}
                        {%- if initial_secondary_image.width >= 713 -%}{{ initial_secondary_image | image_url: width: 713 }} 713w,{%- endif -%}
                        {%- if initial_secondary_image.width >= 823 -%}{{ initial_secondary_image | image_url: width: 823 }} 823w,{%- endif -%}
                        {%- if initial_secondary_image.width >= 990 -%}{{ initial_secondary_image | image_url: width: 990 }} 990w,{%- endif -%}
                        {%- if initial_secondary_image.width >= 1100 -%}{{ initial_secondary_image | image_url: width: 1100 }} 1100w,{%- endif -%}
                        {%- if initial_secondary_image.width >= 1206 -%}{{ initial_secondary_image | image_url: width: 1206 }} 1206w,{%- endif -%}
                        {%- if initial_secondary_image.width >= 1346 -%}{{ initial_secondary_image | image_url: width: 1346 }} 1346w,{%- endif -%}
                        {%- if initial_secondary_image.width >= 1426 -%}{{ initial_secondary_image | image_url: width: 1426 }} 1426w,{%- endif -%}
                        {%- if initial_secondary_image.width >= 1646 -%}{{ initial_secondary_image | image_url: width: 1646 }} 1646w,{%- endif -%}
                        {%- if initial_secondary_image.width >= 1946 -%}{{ initial_secondary_image | image_url: width: 1946 }} 1946w,{%- endif -%}
                        {{ initial_secondary_image | image_url }} {{ initial_secondary_image.width }}w
                      "
                      sizes="(min-width: 1200px) 715px, (min-width: 990px) calc(65.0vw - 10rem), (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw / 1 - 4rem)"
                      loading="lazy"
                      width="{{ initial_secondary_image.width }}"
                      height="{{ initial_secondary_image.height }}"
                      alt="{{ selected_variant.title | append: ' - Secondary image' | escape }}"
                    >
                  {%- endif -%}
                </div>
                <button
                  class="product__media-toggle quick-add-hidden product__media-zoom-{{ section.settings.image_zoom }}"
                  type="button"
                  aria-haspopup="dialog"
                  data-media-id="{{ section.id }}-secondary-slot"
                >
                  <span class="visually-hidden">
                    {{ 'products.product.media.open_media' | t: index: media_position }}
                  </span>
                </button>
              </modal-opener>
            {%- endif -%}
          </div>
        </li>
      {%- endif -%}

      {%- comment -%}
        3. Then render remaining product media (excluding already rendered featured media)
      {%- endcomment -%}
      {%- for media in product.media -%}
        {% if media_position >= limit
          or media_position >= 1
          and section.settings.hide_variants
          and variant_images contains media.src
        %}
          {% continue %}
        {% endif %}

        {%- comment -%}Skip if this media was already rendered as featured media{%- endcomment -%}
        {%- unless rendered_media_ids contains media.id -%}
          <li
            id="Slide-{{ section.id }}-{{ media.id }}"
            class="product__media-item grid__item slider__slide{% if single_media_visible %} product__media-item--single{% endif %}{% if selected_variant.featured_media == nil and forloop.index == 1 %} is-active{% endif %}{% if media.media_type != 'image' %} product__media-item--full{% endif %}{% if settings.animations_reveal_on_scroll %} scroll-trigger animate--fade-in{% endif %}"
            data-media-id="{{ section.id }}-{{ media.id }}"
          >
            {%- liquid
              assign media_position = media_position | default: 0 | plus: 1
              assign lazy_load = true
            -%}
            {% render 'product-thumbnail',
              media: media,
              media_count: media_count,
              position: media_position,
              desktop_layout: section.settings.gallery_layout,
              mobile_layout: section.settings.mobile_thumbnails,
              loop: section.settings.enable_video_looping,
              modal_id: section.id,
              xr_button: true,
              media_width: media_width,
              media_fit: section.settings.media_fit,
              constrain_to_viewport: section.settings.constrain_to_viewport,
              lazy_load: lazy_load
            %}
          </li>
        {%- endunless -%}
      {%- endfor -%}




    </ul>
    <div class="slider-buttons quick-add-hidden{% if hide_mobile_slider %} small-hide{% endif %}">
      <button
        type="button"
        class="slider-button slider-button--prev"
        name="previous"
        aria-label="{{ 'general.slider.previous_slide' | t }}"
      >
        <span class="svg-wrapper">
          {{- 'icon-caret.svg' | inline_asset_content -}}
        </span>
      </button>
      <div class="slider-counter caption">
        <span class="slider-counter--current">1</span>
        <span aria-hidden="true"> / </span>
        <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
        <span class="slider-counter--total">{{ media_count }}</span>
      </div>
      <button
        type="button"
        class="slider-button slider-button--next"
        name="next"
        aria-label="{{ 'general.slider.next_slide' | t }}"
      >
        <span class="svg-wrapper">
          {{- 'icon-caret.svg' | inline_asset_content -}}
        </span>
      </button>
    </div>
  </slider-component>
  {%- if first_3d_model -%}
    <button
      class="button button--full-width product__xr-button"
      type="button"
      aria-label="{{ 'products.product.xr_button_label' | t }}"
      data-shopify-xr
      data-shopify-model3d-id="{{ first_3d_model.id }}"
      data-shopify-title="{{ product.title | escape }}"
      data-shopify-xr-hidden
    >
      <span class="svg-wrapper">
        {{- 'icon-3d-model.svg' | inline_asset_content -}}
      </span>
      {{ 'products.product.xr_button' | t }}
    </button>
  {%- endif -%}
  {%- liquid
    assign is_not_limited_to_single_item = false
    if limit == null or limit > 1
      assign is_not_limited_to_single_item = true
    endif
  -%}
  {%- if is_not_limited_to_single_item
    and media_count > 1
    and section.settings.gallery_layout contains 'thumbnail'
    or section.settings.mobile_thumbnails == 'show'
  -%}
    <slider-component
      id="GalleryThumbnails-{{ section.id }}"
      class="thumbnail-slider slider-mobile-gutter quick-add-hidden{% unless section.settings.gallery_layout contains 'thumbnail' %} medium-hide large-up-hide{% endunless %}{% if section.settings.mobile_thumbnails != 'show' %} small-hide{% endif %}{% if media_count <= 3 %} thumbnail-slider--no-slide{% endif %}"
    >
      <button
        type="button"
        class="slider-button slider-button--prev{% if media_count <= 3 %} small-hide{% endif %}{% if media_count <= 4 %} medium-hide large-up-hide{% endif %}"
        name="previous"
        aria-label="{{ 'general.slider.previous_slide' | t }}"
        aria-controls="GalleryThumbnails-{{ section.id }}"
        data-step="3"
      >
        <span class="svg-wrapper">
          {{- 'icon-caret.svg' | inline_asset_content -}}
        </span>
      </button>
      <ul
        id="Slider-Thumbnails-{{ section.id }}"
        class="thumbnail-list list-unstyled slider slider--mobile{% if section.settings.gallery_layout == 'thumbnail_slider' %} slider--tablet-up{% endif %}"
      >
        {%- capture sizes -%}
          (min-width: {{ settings.page_width }}px) calc(({{ settings.page_width | minus: 100 | times: media_width | round }} - 4rem) / 4),
          (min-width: 990px) calc(({{ media_width | times: 100 }}vw - 4rem) / 4),
          (min-width: 750px) calc((100vw - 15rem) / 8),
          calc((100vw - 8rem) / 3)
        {%- endcapture -%}

        {%- comment -%}
          1. First render thumbnail for selected variant's featured image
        {%- endcomment -%}
        {%- if featured_media != null -%}
          {%- liquid
            capture media_index
              if featured_media.media_type == 'model'
                increment model_index
              elsif featured_media.media_type == 'video' or featured_media.media_type == 'external_video'
                increment video_index
              elsif featured_media.media_type == 'image'
                increment image_index
              endif
            endcapture
            assign media_index = media_index | plus: 1
          -%}
          <li
            id="Slide-Thumbnails-{{ section.id }}-0"
            class="thumbnail-list__item slider__slide{% if section.settings.hide_variants and variant_images contains featured_media.src %} thumbnail-list_item--variant{% endif %}"
            data-target="{{ section.id }}-{{ featured_media.id }}"
            data-media-position="{{ media_index }}"
          >
            {%- capture thumbnail_id -%}
              Thumbnail-{{ section.id }}-0
            {%- endcapture -%}
            <button
              class="thumbnail global-media-settings global-media-settings--no-shadow"
              aria-label="{%- if featured_media.media_type == 'image' -%}{{ 'products.product.media.load_image' | t: index: media_index }}{%- elsif featured_media.media_type == 'model' -%}{{ 'products.product.media.load_model' | t: index: media_index }}{%- elsif featured_media.media_type == 'video' or featured_media.media_type == 'external_video' -%}{{ 'products.product.media.load_video' | t: index: media_index }}{%- endif -%}"
              aria-current="true"
              aria-controls="GalleryViewer-{{ section.id }}"
              aria-describedby="{{ thumbnail_id }}"
            >
              {{
                featured_media.preview_image
                | image_url: width: 416
                | image_tag:
                  loading: 'lazy',
                  sizes: sizes,
                  widths: '54, 74, 104, 162, 208, 324, 416',
                  id: thumbnail_id,
                  alt: featured_media.alt
                | escape
              }}
            </button>
          </li>
        {%- endif -%}


        {%- comment -%}
          3. Then render remaining product media thumbnails (excluding already rendered featured media)
        {%- endcomment -%}
        {%- for media in product.media -%}
          {%- unless media.id == featured_media.id -%}
            {%- liquid
              capture media_index
                if media.media_type == 'model'
                  increment model_index
                elsif media.media_type == 'video' or media.media_type == 'external_video'
                  increment video_index
                elsif media.media_type == 'image'
                  increment image_index
                endif
              endcapture
              assign media_index = media_index | plus: 1
            -%}
            <li
              id="Slide-Thumbnails-{{ section.id }}-{{ forloop.index }}"
              class="thumbnail-list__item slider__slide{% if section.settings.hide_variants and variant_images contains media.src %} thumbnail-list_item--variant{% endif %}"
              data-target="{{ section.id }}-{{ media.id }}"
              data-media-position="{{ media_index }}"
            >
              {%- if media.media_type == 'model' -%}
                <span class="thumbnail__badge" aria-hidden="true">
                  <span class="svg-wrapper">
                    {{- 'icon-3d-model.svg' | inline_asset_content -}}
                  </span>
                </span>
              {%- elsif media.media_type == 'video' or media.media_type == 'external_video' -%}
                <span class="thumbnail__badge" aria-hidden="true">
                  <span class="svg-wrapper">
                    {{- 'icon-play.svg' | inline_asset_content -}}
                  </span>
                </span>
              {%- endif -%}
              {%- capture thumbnail_id -%}
                Thumbnail-{{ section.id }}-{{ forloop.index }}
              {%- endcapture -%}
              <button
                class="thumbnail global-media-settings global-media-settings--no-shadow"
                aria-label="{%- if media.media_type == 'image' -%}{{ 'products.product.media.load_image' | t: index: media_index }}{%- elsif media.media_type == 'model' -%}{{ 'products.product.media.load_model' | t: index: media_index }}{%- elsif media.media_type == 'video' or media.media_type == 'external_video' -%}{{ 'products.product.media.load_video' | t: index: media_index }}{%- endif -%}"
                {% if media == product.selected_or_first_available_variant.featured_media
                  or product.selected_or_first_available_variant.featured_media == null
                  and forloop.index == 1
                %}
                  aria-current="true"
                {% endif %}
                aria-controls="GalleryViewer-{{ section.id }}"
                aria-describedby="{{ thumbnail_id }}"
              >
                {{
                  media.preview_image
                  | image_url: width: 416
                  | image_tag:
                    loading: 'lazy',
                    sizes: sizes,
                    widths: '54, 74, 104, 162, 208, 324, 416',
                    id: thumbnail_id,
                    alt: media.alt
                  | escape
                }}
              </button>
            </li>
          {%- endunless -%}
        {%- endfor -%}

        {%- comment -%}
          4. Finally, render thumbnails for all variants' secondary images (hidden by default, except selected variant)
        {%- endcomment -%}
        {%- for variant in product.variants -%}
          {%- if variant.metafields.custom.secondary_image -%}
              {%- liquid
                assign secondary_media_position = 2
                assign secondary_image = variant.metafields.custom.secondary_image
                capture thumbnail_id
                  echo 'Thumbnail-'
                  echo section.id
                  echo '-variant-'
                  echo variant.id
                  echo '-secondary'
                endcapture
              -%}
              <li
                id="Slide-Thumbnails-{{ section.id }}-variant-{{ variant.id }}-secondary"
                class="thumbnail-list__item slider__slide variant-secondary-thumbnail{% if variant.id == selected_variant.id %} show{% endif %}"
                data-target="{{ section.id }}-secondary-slot"
                data-media-position="{{ secondary_media_position }}"
                data-variant-id="{{ variant.id }}"
              >
              <button
                class="thumbnail global-media-settings global-media-settings--no-shadow"
                aria-label="{{ 'products.product.media.load_image' | t: index: secondary_media_position }}"
                aria-controls="GalleryViewer-{{ section.id }}"
                aria-describedby="{{ thumbnail_id }}"
              >
                {%- if secondary_image.media_type -%}
                  {%- comment -%}Use media_image filter for MediaImage metafields{%- endcomment -%}
                  {{ secondary_image | media_image: class: 'media--adapt', loading: 'lazy', sizes: '54px', widths: '54, 74, 104, 162, 208, 324, 416' }}
                {%- else -%}
                  {%- comment -%}Manual responsive image for File URL metafields{%- endcomment -%}
                  <img
                    src="{{ secondary_image | image_url: width: 416 }}"
                    srcset="
                      {%- if secondary_image.width >= 54 -%}{{ secondary_image | image_url: width: 54 }} 54w,{%- endif -%}
                      {%- if secondary_image.width >= 74 -%}{{ secondary_image | image_url: width: 74 }} 74w,{%- endif -%}
                      {%- if secondary_image.width >= 104 -%}{{ secondary_image | image_url: width: 104 }} 104w,{%- endif -%}
                      {%- if secondary_image.width >= 162 -%}{{ secondary_image | image_url: width: 162 }} 162w,{%- endif -%}
                      {%- if secondary_image.width >= 208 -%}{{ secondary_image | image_url: width: 208 }} 208w,{%- endif -%}
                      {%- if secondary_image.width >= 324 -%}{{ secondary_image | image_url: width: 324 }} 324w,{%- endif -%}
                      {%- if secondary_image.width >= 416 -%}{{ secondary_image | image_url: width: 416 }} 416w,{%- endif -%}
                      {{ secondary_image | image_url }} {{ secondary_image.width }}w
                    "
                    sizes="54px"
                    loading="lazy"
                    width="54"
                    height="54"
                    id="{{ thumbnail_id }}"
                    alt="{{ variant.title | append: ' - Secondary image' | escape }}"
                  >
                {%- endif -%}
              </button>
            </li>
          {%- endif -%}
        {%- endunless -%}
        {%- endfor -%}
      </ul>
      <button
        type="button"
        class="slider-button slider-button--next{% if media_count <= 3 %} small-hide{% endif %}{% if media_count <= 4 %} medium-hide large-up-hide{% endif %}"
        name="next"
        aria-label="{{ 'general.slider.next_slide' | t }}"
        aria-controls="GalleryThumbnails-{{ section.id }}"
        data-step="3"
      >
        <span class="svg-wrapper">
          {{- 'icon-caret.svg' | inline_asset_content -}}
        </span>
      </button>
    </slider-component>
  {%- endif -%}
</media-gallery>
