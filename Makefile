STORE = wildbreedco.myshopify.com
DEV_THEME_ID = 145771167884
LIVE_THEME_ID = 145577705612

.DEFAULT_GOAL := help

help:
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | \
		awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-16s\033[0m %s\n", $$1, $$2}'

list:           ## List the themes from Shopify
	shopify theme list --store $(STORE)
pull-dev:       ## Pull the dev theme from Shopify
	shopify theme pull --store $(STORE) --theme $(DEV_THEME_ID)

push-dev:       ## Push local changes to dev theme
	shopify theme push --store $(STORE) --theme $(DEV_THEME_ID)

push-live:      ## Push local changes to live theme (use with caution)
	shopify theme push --store $(STORE) --theme $(LIVE_THEME_ID) --allow-live

preview:        ## Preview the dev theme in browser
	shopify theme preview --store $(STORE) --theme $(DEV_THEME_ID)

diff:           ## Compare local files to base Dawn theme
	diff -qr ../dawn-15.3.0 ./ | grep -v '.DS_Store'

lint:           ## Run theme-check linter (requires Shopify CLI)
	shopify theme check

lint-fix:       ## Run theme-check linter with auto-fix (requires Shopify CLI)
	shopify theme check --auto-correct
